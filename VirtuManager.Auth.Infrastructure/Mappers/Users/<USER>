using Riok.Mapperly.Abstractions;
using VirtuManager.Auth.Domain.Entities;
using GrpcUser = VirtuManager.Auth.User;
using GrpcUserRole = VirtuManager.Auth.UserRole;
using DomainUserRole = VirtuManager.Auth.Domain.Enums.UserRole;

namespace VirtuManager.Auth.Infrastructure.Mappers.Users;

[Mapper]
public partial class UserGrpcMappingProfile
{
    /// <summary>
    /// Maps Domain User entity to gRPC User model
    /// </summary>
    [MapProperty(nameof(User.Id), nameof(GrpcUser.Guid))]
    [MapperIgnoreSource(nameof(User.PasswordHash))]
    [MapperIgnoreSource(nameof(User.RefreshTokenHash))]
    [MapperIgnoreSource(nameof(User.CreatedAt))]
    [MapperIgnoreSource(nameof(User.UpdatedAt))]
    [MapperIgnoreSource(nameof(User.LastLoginAt))]
    [MapperIgnoreSource(nameof(User.LastPasswordChangeAt))]
    public partial GrpcUser MapToGrpcModel(User user);

    /// <summary>
    /// Maps gRPC User model to Domain User entity
    /// </summary>
    [MapProperty(nameof(GrpcUser.Guid), nameof(User.Id))]
    public partial User MapToDomainEntity(GrpcUser grpcUser);

    /// <summary>
    /// Custom mapping for Guid to string conversion
    /// </summary>
    private string MapGuidToString(Guid guid) => guid.ToString();

    /// <summary>
    /// Custom mapping for string to Guid conversion
    /// </summary>
    private Guid MapStringToGuid(string guidString) => Guid.Parse(guidString);

    /// <summary>
    /// Custom mapping for Domain UserRole to gRPC UserRole
    /// </summary>
    private GrpcUserRole MapDomainRoleToGrpcRole(DomainUserRole domainRole) => domainRole switch
    {
        DomainUserRole.Regular => GrpcUserRole.Regular,
        DomainUserRole.Developer => GrpcUserRole.Developer,
        DomainUserRole.Admin => GrpcUserRole.Admin,
        _ => GrpcUserRole.Regular
    };

    /// <summary>
    /// Custom mapping for gRPC UserRole to Domain UserRole
    /// </summary>
    private DomainUserRole MapGrpcRoleToDomainRole(GrpcUserRole grpcRole) => grpcRole switch
    {
        GrpcUserRole.Regular => DomainUserRole.Regular,
        GrpcUserRole.Developer => DomainUserRole.Developer,
        GrpcUserRole.Admin => DomainUserRole.Admin,
        _ => DomainUserRole.Regular
    };
}
