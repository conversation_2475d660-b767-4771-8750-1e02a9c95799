using Microsoft.EntityFrameworkCore;
using VirtuManager.Auth.Domain.Entities.Base;
using VirtuManager.Auth.Domain.Pagination;
using VirtuManager.Auth.Domain.Repositories.Abstractions;
using VirtuManager.Auth.Infrastructure.Database;

namespace VirtuManager.Auth.Infrastructure.Repositories;

public class GenericRepository<TEntity, TKey> : IRepository<TEntity, TKey>
    where TKey : IEquatable<TKey> where TEntity : class, IEntity<TKey>
{
    protected readonly ApplicationDbContext Context;

    public GenericRepository(ApplicationDbContext context)
    {
        ArgumentNullException.ThrowIfNull(context);
        Context = context;
    }

    public virtual async Task AddAsync(TEntity entity, CancellationToken cancellationToken = default)
    {
        await Context.Set<TEntity>().AddAsync(entity, cancellationToken);
        await Context.SaveChangesAsync(cancellationToken);
    }

    public virtual async Task RemoveAsync(TEntity entity, CancellationToken cancellationToken = default)
    {
        Context.Set<TEntity>().Remove(entity);
        await Context.SaveChangesAsync(cancellationToken);
    }

    public virtual async Task UpdateRange(IEnumerable<TEntity> entities)
    {
        Context.Set<TEntity>().UpdateRange(entities);
        await Context.SaveChangesAsync();
    }

    public virtual async Task UpdateAsync(TEntity entity, CancellationToken cancellationToken = default)
    {
        Context.Set<TEntity>().Update(entity);
        await Context.SaveChangesAsync(cancellationToken);
    }

    public virtual async Task<TEntity?> GetByIdAsync(TKey id, CancellationToken cancellationToken = default)
        => await Context.Set<TEntity>().FirstOrDefaultAsync(x => x.Id.Equals(id), cancellationToken);

    public virtual async Task<List<TEntity>?> GetAllAsync(CancellationToken cancellationToken = default)
        => await Context.Set<TEntity>().ToListAsync(cancellationToken);

    public virtual async Task<PagedResult<TEntity>> GetPagedAsync(Pageable pageable,
        CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(pageable);

        var query = Context.Set<TEntity>().AsQueryable();

        var pageNumber = Math.Max(1, pageable.PageNumber);
        var pageSize = Math.Clamp(pageable.PageSize, 1, 1000);

        var totalCount = await query.CountAsync(cancellationToken);

        var items = await query
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return new PagedResult<TEntity>(items, totalCount);
    }
}