using VirtuManager.Auth.Domain.Repositories.Abstractions;
using VirtuManager.Auth.Infrastructure.Database;

namespace VirtuManager.Auth.Infrastructure.Repositories;

public class UnitOfWork : IUnitOfWork
{
    private readonly ApplicationDbContext _context;
    private bool _disposed = false;

    public UnitOfWork(ApplicationDbContext context)
    {
        ArgumentNullException.ThrowIfNull(context, nameof(context));
        _context = context;
    }
    
    public async Task<ITransaction> BeginTransactionAsync(CancellationToken cancellationToken = default)
    {
        ThrowIfDisposed();
        var dbContextTransaction = await _context.Database.BeginTransactionAsync(cancellationToken);
        return new Transaction(dbContextTransaction);
    }
    
    public void Dispose()
    {
        if (_disposed) return;
        _context.Dispose();
        _disposed = true;
    }
    
    private void ThrowIfDisposed()
    {
        if (!_disposed) return;
        throw new ObjectDisposedException(nameof(UnitOfWork));
    }
}