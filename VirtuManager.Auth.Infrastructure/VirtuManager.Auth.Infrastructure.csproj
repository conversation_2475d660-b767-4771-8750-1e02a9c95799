<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
      <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.16" />
      <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.16">
        <PrivateAssets>all</PrivateAssets>
        <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      </PackageReference>
      <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.11" />
      <PackageReference Include="Riok.Mapperly" Version="4.2.1" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\VirtuManager.Auth.Application\VirtuManager.Auth.Application.csproj" />
      <ProjectReference Include="..\VirtuManager.Auth.Protos\VirtuManager.Auth.Protos.csproj" />
    </ItemGroup>

</Project>
