using VirtuManager.Auth.Application.ViewModels;
using VirtuManager.Auth.Domain.Entities;
using VirtuManager.Auth.Infrastructure.Mappers.Users;
using GrpcUser = VirtuManager.Auth.User;
using GrpcGetUsersResponse = VirtuManager.Auth.GetUsersResponse;
using GrpcGetUsersPagedResponse = VirtuManager.Auth.GetUsersPagedResponse;

namespace VirtuManager.Auth.Infrastructure.Services;

/// <summary>
/// Service for mapping between Domain entities and gRPC models
/// </summary>
public interface IUserGrpcMappingService
{
    /// <summary>
    /// Maps Domain User entity to gRPC User model
    /// </summary>
    GrpcUser MapToGrpcModel(User user);
    
    /// <summary>
    /// Maps collection of Domain User entities to gRPC User models
    /// </summary>
    IEnumerable<GrpcUser> MapToGrpcModels(IEnumerable<User> users);
    
    /// <summary>
    /// Maps gRPC User model to Domain User entity
    /// </summary>
    User MapToDomainEntity(GrpcUser grpcUser);
    
    /// <summary>
    /// Maps collection of gRPC User models to Domain User entities
    /// </summary>
    IEnumerable<User> MapToDomainEntities(IEnumerable<GrpcUser> grpcUsers);
    
    /// <summary>
    /// Maps UserViewModel to gRPC User model
    /// </summary>
    GrpcUser MapViewModelToGrpcModel(UserViewModel userViewModel);
    
    /// <summary>
    /// Maps collection of UserViewModels to gRPC User models
    /// </summary>
    IEnumerable<GrpcUser> MapViewModelsToGrpcModels(IEnumerable<UserViewModel> userViewModels);
    
    /// <summary>
    /// Maps collection of users to gRPC GetUsersResponse
    /// </summary>
    GrpcGetUsersResponse MapToGetUsersResponse(IEnumerable<User> users);
    
    /// <summary>
    /// Maps collection of user view models to gRPC GetUsersResponse
    /// </summary>
    GrpcGetUsersResponse MapViewModelsToGetUsersResponse(IEnumerable<UserViewModel> userViewModels);
    
    /// <summary>
    /// Maps paginated users to gRPC GetUsersPagedResponse
    /// </summary>
    GrpcGetUsersPagedResponse MapToGetUsersPagedResponse(
        IEnumerable<User> users,
        long totalCount,
        int pageNumber,
        int pageSize);
    
    /// <summary>
    /// Maps paginated user view models to gRPC GetUsersPagedResponse
    /// </summary>
    GrpcGetUsersPagedResponse MapViewModelsToGetUsersPagedResponse(
        IEnumerable<UserViewModel> userViewModels,
        long totalCount,
        int pageNumber,
        int pageSize);
}

public class UserGrpcMappingService : IUserGrpcMappingService
{
    private readonly UserGrpcMapper _grpcMapper;
    private readonly UserGrpcReverseMapper _reverseMapper;

    public UserGrpcMappingService(UserGrpcMapper grpcMapper, UserGrpcReverseMapper reverseMapper)
    {
        ArgumentNullException.ThrowIfNull(grpcMapper, nameof(grpcMapper));
        ArgumentNullException.ThrowIfNull(reverseMapper, nameof(reverseMapper));
        
        _grpcMapper = grpcMapper;
        _reverseMapper = reverseMapper;
    }

    public GrpcUser MapToGrpcModel(User user) => _grpcMapper.MapToModel(user);

    public IEnumerable<GrpcUser> MapToGrpcModels(IEnumerable<User> users) => _grpcMapper.MapToModel(users);

    public User MapToDomainEntity(GrpcUser grpcUser) => _reverseMapper.MapToModel(grpcUser);

    public IEnumerable<User> MapToDomainEntities(IEnumerable<GrpcUser> grpcUsers) => _reverseMapper.MapToModel(grpcUsers);

    public GrpcUser MapViewModelToGrpcModel(UserViewModel userViewModel)
    {
        return new GrpcUser
        {
            Guid = userViewModel.Id.ToString(),
            Login = userViewModel.Login,
            Email = userViewModel.Email,
            Role = (VirtuManager.Auth.UserRole)userViewModel.Role,
        };
    }

    public IEnumerable<GrpcUser> MapViewModelsToGrpcModels(IEnumerable<UserViewModel> userViewModels)
        => userViewModels.Select(MapViewModelToGrpcModel);

    public GrpcGetUsersResponse MapToGetUsersResponse(IEnumerable<User> users)
    {
        var response = new GrpcGetUsersResponse();
        response.Users.AddRange(MapToGrpcModels(users));
        return response;
    }

    public GrpcGetUsersResponse MapViewModelsToGetUsersResponse(IEnumerable<UserViewModel> userViewModels)
    {
        var response = new GrpcGetUsersResponse();
        response.Users.AddRange(MapViewModelsToGrpcModels(userViewModels));
        return response;
    }

    public GrpcGetUsersPagedResponse MapToGetUsersPagedResponse(
        IEnumerable<User> users,
        long totalCount,
        int pageNumber,
        int pageSize)
    {
        var totalPages = Math.Ceiling((double)totalCount / pageSize);
        
        var response = new GrpcGetUsersPagedResponse
        {
            TotalCount = totalCount,
            PageNumber = pageNumber,
            PageSize = pageSize,
            TotalPages = totalPages
        };
        
        response.Users.AddRange(MapToGrpcModels(users));
        return response;
    }

    public GrpcGetUsersPagedResponse MapViewModelsToGetUsersPagedResponse(
        IEnumerable<UserViewModel> userViewModels,
        long totalCount,
        int pageNumber,
        int pageSize)
    {
        var totalPages = Math.Ceiling((double)totalCount / pageSize);
        
        var response = new GrpcGetUsersPagedResponse
        {
            TotalCount = totalCount,
            PageNumber = pageNumber,
            PageSize = pageSize,
            TotalPages = totalPages
        };
        
        response.Users.AddRange(MapViewModelsToGrpcModels(userViewModels));
        return response;
    }
}
