using Grpc.Core;
using Microsoft.AspNetCore.Authorization;
using VirtuManager.Auth;
using VirtuManager.Auth.Api.Services.Interfaces;
using VirtuManager.Auth.Infrastructure.Services;
using Google.Protobuf.WellKnownTypes;

namespace VirtuManager.Auth.Api.Services.Grpc.Examples;

/// <summary>
/// Example of enhanced gRPC service using the structured mapping approach
/// This demonstrates how to use the new mapping services
/// </summary>
[Authorize]
public class EnhancedGetAllUsersService : GrpcUserService.GrpcUserServiceBase
{
    private readonly IGetUsersService _getUsersService;
    private readonly IUserGrpcMappingService _mappingService;
    private readonly ILogger<EnhancedGetAllUsersService> _logger;
    
    public EnhancedGetAllUsersService(
        IGetUsersService getUsersService,
        IUserGrpcMappingService mappingService,
        ILogger<EnhancedGetAllUsersService> logger)
    {
        ArgumentNullException.ThrowIfNull(getUsersService, nameof(getUsersService));
        ArgumentNullException.ThrowIfNull(mappingService, nameof(mappingService));
        ArgumentNullException.ThrowIfNull(logger, nameof(logger));
        
        _getUsersService = getUsersService;
        _mappingService = mappingService;
        _logger = logger;
    }

    public override async Task<GetUsersResponse> GetAllUsers(Empty request, ServerCallContext context)
    {
        try
        {
            _logger.LogInformation("Getting all users via enhanced gRPC service");
            
            var users = await _getUsersService.GetAll();
            
            // Use the structured mapping service
            var response = _mappingService.MapViewModelsToGetUsersResponse(users);
            
            _logger.LogInformation("Successfully retrieved {UserCount} users", response.Users.Count);
            
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting all users");
            throw new RpcException(new Status(StatusCode.Internal, "Internal server error"));
        }
    }

    public override async Task<GetUsersPagedResponse> GetUsersPaged(GetUsersPagedRequest request, ServerCallContext context)
    {
        try
        {
            _logger.LogInformation("Getting paged users: Page {PageNumber}, Size {PageSize}", 
                request.PageNumber, request.PageSize);
            
            // Note: This would require implementing a paged service method
            // For demonstration purposes, we'll simulate pagination
            var allUsers = await _getUsersService.GetAll();
            var pagedUsers = allUsers
                .Skip((request.PageNumber - 1) * request.PageSize)
                .Take(request.PageSize);
            
            var totalCount = allUsers.Count();
            
            // Use the structured mapping service for pagination
            var response = _mappingService.MapViewModelsToGetUsersPagedResponse(
                pagedUsers, 
                totalCount, 
                request.PageNumber, 
                request.PageSize);
            
            _logger.LogInformation("Successfully retrieved page {PageNumber} with {UserCount} users", 
                request.PageNumber, response.Users.Count);
            
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting paged users");
            throw new RpcException(new Status(StatusCode.Internal, "Internal server error"));
        }
    }
}
