FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
USER $APP_UID
WORKDIR /app
EXPOSE 5141
ENV ASPNETCORE_URLS=http://+:5141

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["virtu-manager-auth.csproj", "virtu-manager-auth/"]
RUN dotnet restore "virtu-manager-auth/virtu-manager-auth.csproj"
COPY . ./virtu-manager-auth/
WORKDIR "/src/virtu-manager-auth"
RUN dotnet build "virtu-manager-auth.csproj" -c $BUILD_CONFIGURATION -o /app/build

FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "virtu-manager-auth.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
# COPY ./.env /app/.env
ENTRYPOINT ["dotnet", "virtu-manager-auth.dll"]
