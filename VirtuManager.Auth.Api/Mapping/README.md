# gRPC User Mapping Documentation

This document explains how to use the structured mapping system for gRPC User models in the VirtuManager Auth Service.

## Overview

The mapping system provides multiple approaches for converting between Domain entities, ViewModels, and gRPC models:

1. **Extension Methods** (Simple, backward compatible)
2. **Structured Mappers** (Type-safe, dependency injectable)
3. **Mapping Service** (Comprehensive, service-oriented)

## Architecture

```
Domain.Entities.User ←→ gRPC.User
       ↕                    ↕
Application.ViewModels.UserViewModel
```

## Components

### 1. Mapping Profiles (Infrastructure Layer)

- `UserGrpcMappingProfile` - Riok.Mapperly-based mapping configuration
- Handles automatic property mapping with custom conversions
- Ignores sensitive fields (PasswordHash, RefreshTokenHash)

### 2. Structured Mappers (Infrastructure Layer)

- `UserGrpcMapper` - Maps Domain.User → gRPC.User
- `UserGrpcReverseMapper` - Maps gRPC.User → Domain.User
- Implements `IMapper<TDestination, TSource>` interface

### 3. Extension Methods (API Layer)

- `UserMappingExtensions` - Basic mapping extensions
- `UserGrpcCollectionExtensions` - Collection and pagination mapping
- Provides both manual and mapper-based approaches

### 4. Mapping Service (Infrastructure Layer)

- `IUserGrpcMappingService` / `UserGrpcMappingService`
- Comprehensive service for all mapping operations
- Injectable dependency for services

## Usage Examples

### 1. Simple Extension Methods (Backward Compatible)

```csharp
// Domain entity to gRPC model
var grpcUser = domainUser.ToGrpcModel();

// ViewModel to gRPC model
var grpcUser = userViewModel.ToGrpcModel();

// gRPC model to Domain entity
var domainUser = grpcUser.ToDomainEntity();
```

### 2. Using Structured Mappers

```csharp
public class MyService
{
    private readonly UserGrpcMapper _mapper;
    
    public MyService(UserGrpcMapper mapper)
    {
        _mapper = mapper;
    }
    
    public GrpcUser ConvertUser(User domainUser)
    {
        return _mapper.MapToModel(domainUser);
    }
    
    public List<GrpcUser> ConvertUsers(IEnumerable<User> domainUsers)
    {
        return _mapper.MapToModel(domainUsers);
    }
}
```

### 3. Using Mapping Service (Recommended)

```csharp
public class EnhancedUserService : GrpcUserService.GrpcUserServiceBase
{
    private readonly IUserGrpcMappingService _mappingService;
    private readonly IGetUsersService _usersService;
    
    public EnhancedUserService(
        IUserGrpcMappingService mappingService,
        IGetUsersService usersService)
    {
        _mappingService = mappingService;
        _usersService = usersService;
    }
    
    public override async Task<GetUsersResponse> GetAllUsers(Empty request, ServerCallContext context)
    {
        var users = await _usersService.GetAll();
        return _mappingService.MapViewModelsToGetUsersResponse(users);
    }
    
    public override async Task<GetUsersPagedResponse> GetUsersPaged(GetUsersPagedRequest request, ServerCallContext context)
    {
        var users = await _usersService.GetPaged(request.PageNumber, request.PageSize);
        var totalCount = await _usersService.GetTotalCount();
        
        return _mappingService.MapViewModelsToGetUsersPagedResponse(
            users, totalCount, request.PageNumber, request.PageSize);
    }
}
```

### 4. Collection Mapping

```csharp
// Using extension methods
var response = users.ToGrpcUsersResponse();
var pagedResponse = users.ToGrpcUsersPagedResponse(totalCount, pageNumber, pageSize);

// Using mapper
var response = users.ToGrpcUsersResponse(mapper);
var pagedResponse = users.ToGrpcUsersPagedResponse(totalCount, pageNumber, pageSize, mapper);
```

## Registration in DI Container

Add to your `Program.cs`:

```csharp
// Register mapping services
builder.Services.AddUserMappingServices();
```

This registers:
- All mapping profiles
- Structured mappers
- Mapping service interface and implementation

## Benefits

1. **Type Safety** - Compile-time checking of mappings
2. **Performance** - Riok.Mapperly generates optimized mapping code
3. **Maintainability** - Centralized mapping logic
4. **Testability** - Injectable dependencies
5. **Flexibility** - Multiple usage patterns
6. **Backward Compatibility** - Existing code continues to work

## Migration Guide

### From Extension Methods to Mapping Service

**Before:**
```csharp
var response = new GetUsersResponse();
response.Users.AddRange(users.Select(u => u.ToGrpcModel()));
```

**After:**
```csharp
var response = _mappingService.MapViewModelsToGetUsersResponse(users);
```

### Adding New Properties

1. Update the gRPC proto file
2. Regenerate gRPC classes
3. Update `UserGrpcMappingProfile` if needed
4. Custom mappings will be handled automatically

## Best Practices

1. **Use Mapping Service** for new gRPC services
2. **Keep Extension Methods** for simple, one-off conversions
3. **Test Mappings** thoroughly, especially custom conversions
4. **Handle Null Values** appropriately in custom mapping logic
5. **Document Custom Mappings** when business logic is involved

## Performance Considerations

- Riok.Mapperly generates compile-time optimized code
- No reflection overhead
- Minimal memory allocations
- Suitable for high-throughput scenarios
