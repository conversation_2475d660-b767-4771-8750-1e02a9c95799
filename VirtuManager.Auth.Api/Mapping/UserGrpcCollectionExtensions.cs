using VirtuManager.Auth.Application.ViewModels;
using VirtuManager.Auth.Domain.Entities;
using VirtuManager.Auth.Infrastructure.Mappers.Users;
using GrpcUser = VirtuManager.Auth.User;
using GrpcGetUsersResponse = VirtuManager.Auth.GetUsersResponse;
using GrpcGetUsersPagedResponse = VirtuManager.Auth.GetUsersPagedResponse;

namespace virtu_manager_auth.Mapping;

/// <summary>
/// Extension methods for mapping collections of users to gRPC models
/// </summary>
public static class UserGrpcCollectionExtensions
{
    /// <summary>
    /// Maps a collection of Domain User entities to gRPC GetUsersResponse
    /// </summary>
    public static GrpcGetUsersResponse ToGrpcUsersResponse(this IEnumerable<User> users)
    {
        var response = new GrpcGetUsersResponse();
        response.Users.AddRange(users.Select(u => u.ToGrpcModel()));
        return response;
    }

    /// <summary>
    /// Maps a collection of Domain User entities to gRPC GetUsersResponse using structured mapper
    /// </summary>
    public static GrpcGetUsersResponse ToGrpcUsersResponse(this IEnumerable<User> users, UserGrpcMapper mapper)
    {
        ArgumentNullException.ThrowIfNull(mapper, nameof(mapper));
        var response = new GrpcGetUsersResponse();
        response.Users.AddRange(mapper.MapToModel(users));
        return response;
    }

    /// <summary>
    /// Maps a collection of UserViewModel to gRPC GetUsersResponse
    /// </summary>
    public static GrpcGetUsersResponse ToGrpcUsersResponse(this IEnumerable<UserViewModel> userViewModels)
    {
        var response = new GrpcGetUsersResponse();
        response.Users.AddRange(userViewModels.Select(u => u.ToGrpcModel()));
        return response;
    }

    /// <summary>
    /// Maps paginated users to gRPC GetUsersPagedResponse
    /// </summary>
    public static GrpcGetUsersPagedResponse ToGrpcUsersPagedResponse(
        this IEnumerable<User> users,
        long totalCount,
        int pageNumber,
        int pageSize)
    {
        var totalPages = Math.Ceiling((double)totalCount / pageSize);
        
        var response = new GrpcGetUsersPagedResponse
        {
            TotalCount = totalCount,
            PageNumber = pageNumber,
            PageSize = pageSize,
            TotalPages = totalPages
        };
        
        response.Users.AddRange(users.Select(u => u.ToGrpcModel()));
        return response;
    }

    /// <summary>
    /// Maps paginated users to gRPC GetUsersPagedResponse using structured mapper
    /// </summary>
    public static GrpcGetUsersPagedResponse ToGrpcUsersPagedResponse(
        this IEnumerable<User> users,
        long totalCount,
        int pageNumber,
        int pageSize,
        UserGrpcMapper mapper)
    {
        ArgumentNullException.ThrowIfNull(mapper, nameof(mapper));
        
        var totalPages = Math.Ceiling((double)totalCount / pageSize);
        
        var response = new GrpcGetUsersPagedResponse
        {
            TotalCount = totalCount,
            PageNumber = pageNumber,
            PageSize = pageSize,
            TotalPages = totalPages
        };
        
        response.Users.AddRange(mapper.MapToModel(users));
        return response;
    }

    /// <summary>
    /// Maps paginated UserViewModels to gRPC GetUsersPagedResponse
    /// </summary>
    public static GrpcGetUsersPagedResponse ToGrpcUsersPagedResponse(
        this IEnumerable<UserViewModel> userViewModels,
        long totalCount,
        int pageNumber,
        int pageSize)
    {
        var totalPages = Math.Ceiling((double)totalCount / pageSize);
        
        var response = new GrpcGetUsersPagedResponse
        {
            TotalCount = totalCount,
            PageNumber = pageNumber,
            PageSize = pageSize,
            TotalPages = totalPages
        };
        
        response.Users.AddRange(userViewModels.Select(u => u.ToGrpcModel()));
        return response;
    }
}
