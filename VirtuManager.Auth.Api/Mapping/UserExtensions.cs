using VirtuManager.Auth.Application.ViewModels;
using VirtuManager.Auth.Domain.Entities;

namespace virtu_manager_auth.Mapping;

public static class UserMappingExtensions
{
    public static UserViewModel ToViewModel(this User user)
    {
        return new UserViewModel
        {
            Id = user.Id,
            Login = user.Login,
            Email = user.Email,
            Role = user.Role,
            CreatedAt = user.CreatedAt,
            UpdatedAt = user.UpdatedAt,
            LastLoginAt = user.LastLoginAt,
            LastPasswordChangeAt = user.LastPasswordChangeAt
        };
    }

    public static VirtuManager.Auth.User ToGrpcModel(this User user)
    {
        return new VirtuManager.Auth.User
        {
            Guid = user.Id.ToString(),
            Login = user.Login,
            Email = user.Email,
            Role = (VirtuManager.Auth.UserRole)user.Role,
        };
    }
}