using VirtuManager.Auth.Infrastructure.Mappers.Users;
using VirtuManager.Auth.Infrastructure.Services;

namespace VirtuManager.Auth.Api.Extensions.Di;

/// <summary>
/// Extension methods for registering mapping services in DI container
/// </summary>
public static class AddMappingServicesExtensions
{
    /// <summary>
    /// Registers all mapping services for User entities and gRPC models
    /// </summary>
    public static IServiceCollection AddUserMappingServices(this IServiceCollection services)
    {
        // Register Mapperly mapping profiles
        services.AddTransient<UserMappingProfile>();
        services.AddTransient<UserGrpcMappingProfile>();
        
        // Register structured mappers
        services.AddTransient<UserMapper>();
        services.AddTransient<UserGrpcMapper>();
        services.AddTransient<UserGrpcReverseMapper>();
        
        // Register comprehensive mapping service
        services.AddTransient<IUserGrpcMappingService, UserGrpcMappingService>();
        
        return services;
    }
}
