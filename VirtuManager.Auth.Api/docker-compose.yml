services:
  van-auth:
    image: virtu-manager-auth
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "5141:5141"
    depends_on:
      postgres_db:
        condition: service_healthy
      elasticsearch_logs:
        condition: service_started
    networks:
      - app_network

  postgres_db:
    image: postgres:17.2-alpine3.21
    container_name: van_auth_postgres
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB}
    ports:
      - "${POSTGRES_PORT}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - app_network
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}" ]
      interval: 5s
      timeout: 5s
      retries: 5
      start_period: 10s
      
  elasticsearch_logs:
    container_name: virtu-manager-elastic-logs
    image: docker.elastic.co/elasticsearch/elasticsearch:9.0.0
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=true
      - ELASTIC_PASSWORD=C873F52E7F9C74CBBD73262072E29505221DEB12C74ACDE48C11E760B89957DF
    env_file:
      - .env
    command: >
      sh -c "
      echo 'ELASTIC_PASSWORD from .env: '$ELASTIC_PASSWORD;
      /usr/local/bin/docker-entrypoint.sh &
      echo 'Waiting for Elasticsearch to be available...';
      until curl -s -u elastic:$ELASTIC_PASSWORD -o /dev/null -w '%{http_code}' http://localhost:9200 | grep -q '200'; do
        sleep 5;
      done;
      echo 'Elasticsearch is up! Setting password for kibana_system...';
      curl -X POST -u elastic:$ELASTIC_PASSWORD http://localhost:9200/_security/user/kibana_system/_password -H 'Content-Type: application/json' -d '{\"password\": \"$ELASTIC_PASSWORD\"}';
      wait
      "
    mem_limit: 2g
    networks:
      - app_network
    ports:
      - "9200:9200"
    volumes:
      - esdata:/usr/share/elasticsearch/data

  kibana_logs:
    container_name: virtu-manager-kibana
    image: docker.elastic.co/kibana/kibana:9.0.0
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch_logs
    environment:
      - ELASTICSEARCH_HOSTS=elasticsearch_logs
      - ELASTICSEARCH_USERNAME=kibana_system
      - ELASTICSEARCH_PASSWORD=C873F52E7F9C74CBBD73262072E29505221DEB12C74ACDE48C11E760B89957DF
    networks:
      - app_network
  
volumes:
  postgres_data:
  esdata:
    driver: local

networks:
  app_network:
    driver: bridge