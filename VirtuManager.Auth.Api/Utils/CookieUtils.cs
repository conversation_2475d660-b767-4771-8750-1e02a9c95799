using virtu_manager_auth.Utils.Interfaces;

namespace VirtuManager.Auth.Api.Utils;

public class CookieUtils : ICookieUtils
{
    private readonly IConfiguration _configuration;

    public CookieUtils(IConfiguration configuration)
    {
        _configuration = configuration;
    }

    public void SetRefreshTokenCookie(HttpResponse response, string refreshToken) => response.Cookies.Append("refreshToken", refreshToken, GetCookieOptions());

    private CookieOptions GetCookieOptions() => new()
    {
        HttpOnly = _configuration.GetSection("Cookie").GetValue<bool>("HttpOnly"),
        Secure = _configuration.GetSection("Cookie").GetValue<bool>("Secure"),
        SameSite = SameSiteMode.Strict,
        Expires = DateTime.UtcNow.AddDays(_configuration.GetSection("Cookie").GetValue<int>("Expires"))
    };
}