namespace VirtuManager.Auth.Domain.Pagination;

public record PagedResult<T> where T : class
{
    public IEnumerable<T>? Elements { get; }
    
    public long Count { get; }

    public PagedResult(IEnumerable<T>? elements, long count)
    {
        Elements = elements;
        Count = count;
    }

    public PagedResult()
    {
    }

    public void Deconstruct(out IEnumerable<T>? elements, out long count)
    {
        elements = Elements;
        count = Count;
    }
}