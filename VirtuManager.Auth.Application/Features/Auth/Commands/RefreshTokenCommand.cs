using FluentResults;
using MediatR;
using System.ComponentModel.DataAnnotations;

namespace VirtuManager.Auth.Application.Features.Auth.Commands;

public record RefreshTokenCommand : IRequest<Result<RefreshTokenResult>>
{
    [Required]
    public string RefreshToken { get; init; } = string.Empty;
}

public record RefreshTokenResult
{
    public string AccessToken { get; init; } = string.Empty;
    public string RefreshToken { get; init; } = string.Empty;
}
