<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="Google.Protobuf" Version="3.31.1" />
      <PackageReference Include="Grpc.AspNetCore" Version="2.71.0" />
      <PackageReference Include="Grpc.Tools" Version="2.72.0">
        <PrivateAssets>all</PrivateAssets>
        <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      </PackageReference>
    </ItemGroup>

    <ItemGroup>
        <Protobuf Include="Protos\**\*.proto" GrpcServices="Both" />
    </ItemGroup>
    
</Project>
